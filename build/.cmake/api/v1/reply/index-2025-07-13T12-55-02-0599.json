{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "/opt/homebrew/bin/cmake", "cpack": "/opt/homebrew/bin/cpack", "ctest": "/opt/homebrew/bin/ctest", "root": "/opt/homebrew/share/cmake"}, "version": {"isDirty": false, "major": 4, "minor": 0, "patch": 3, "string": "4.0.3", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-99c773f45255d166e536.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "cache-v2-f791d9eb1ea8db4170f3.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-93b20ce780d0e03cca09.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-8441a6c2670a118f698d.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-f791d9eb1ea8db4170f3.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-99c773f45255d166e536.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "toolchains-v1-8441a6c2670a118f698d.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-93b20ce780d0e03cca09.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}]}}}}