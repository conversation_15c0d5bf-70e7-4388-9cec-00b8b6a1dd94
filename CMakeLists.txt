cmake_minimum_required(VERSION 3.16)
project(sdl3_pong C)
set(CMAKE_C_STANDARD 99)

find_package(SDL3 REQUIRED CONFIG)
find_package(SDL3_ttf REQUIRED CONFIG)

# Include directories for stb libraries
include_directories(lib/stb)
include_directories(learn/TTF)
include_directories(learn/TTF/uiText)

# Define source files
set(SOURCES
    # learn/pong/sdl3_pong_default_renderer.c  # Comment out to build GPU version
    # learn/GPUBasic/basic6_TexturedQuad.c
		# learn/GPUBasic/basic7_TexturedAnimatedQuad.c
    # learn/pong/sdl3_pong_GPU.c
    # learn/ECS/ECS.c
    # learn/ECS/testECS.c
		# learn/TTF/testTTF.c
		# learn/TTF/testTTF2D.c

		learn/TTF/uiText/testUIText.c
		learn/TTF/uiText/UIText.c

		  # learn/pong/sdl3_pong_GPU.c

		# learn/ui/buttonVertexColor/testUIButton.c
		# learn/ui/buttonVertexColor/Button.c

		# learn/ui/buttonUniform/testUIButtonUniform.c
		# learn/ui/buttonUniform/ButtonUniform.c

		# learn/ui/buttonText/testUIButtonText.c
		# learn/ui/buttonText/ButtonText.c
		# learn/TTF/uiText/UIText.c
)

add_executable(sdl3_pong ${SOURCES})
target_link_libraries(sdl3_pong PRIVATE SDL3::SDL3 SDL3_ttf)

# Copy assets directory to build directory
add_custom_command(TARGET sdl3_pong POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_directory
    ${CMAKE_SOURCE_DIR}/assets
    $<TARGET_FILE_DIR:sdl3_pong>/assets
)

# Add compiler flags for better debugging and warnings
target_compile_options(sdl3_pong PRIVATE
    -Wall
    -Wextra
    -g
    -O0  # No optimization for debugging
)
