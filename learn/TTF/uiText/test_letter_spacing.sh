#!/bin/bash

# UIText Letter Spacing Test Script
# This script builds and runs the enhanced UIText test program

echo "=== UIText Letter Spacing Test ==="
echo

# Check if we're in the right directory
if [ ! -f "CMakeLists.txt" ]; then
    echo "Error: Please run this script from the project root directory"
    exit 1
fi

# Create build directory if it doesn't exist
if [ ! -d "build" ]; then
    echo "Creating build directory..."
    mkdir build
fi

# Navigate to build directory
cd build

echo "Building the project..."
if [ ! -f "build.ninja" ]; then
    echo "Configuring CMake with Ninja..."
    cmake -G Ninja ..
fi

# Build the project
ninja

if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

echo "Build successful!"
echo
echo "Starting UIText Letter Spacing Test..."
echo
echo "=== Test Instructions ==="
echo "The program will display multiple lines of text with different letter spacing:"
echo "  - Normal Spacing (1.0) - baseline"
echo "  - Tight Spacing (0.7) - closer characters"
echo "  - Loose Spacing (1.3) - wider characters"
echo "  - Very Tight (0.4) - very close characters"
echo "  - Very Loose (1.6) - very wide characters"
echo "  - Animated - sine wave animation"
echo
echo "=== Interactive Controls ==="
echo "  Key 1: Set all text to very tight spacing (0.3)"
echo "  Key 2: Set all text to normal spacing (1.0)"
echo "  Key 3: Set all text to very loose spacing (2.0)"
echo "  Key R: Reset to original spacing values"
echo "  SPACE: Reset animation timer"
echo "  ESC:   Quit the program"
echo
echo "=== Test Scenarios ==="
echo "1. Observe the visual differences between spacing values"
echo "2. Press keys 1, 2, 3 to test uniform spacing changes"
echo "3. Press R to reset and compare original values"
echo "4. Watch the animated text for smooth spacing transitions"
echo "5. Press SPACE to restart the animation"
echo
echo "Press Enter to start the test program..."
read

# Run the test program
./sdl3_pong

echo
echo "Test completed!"
echo
echo "=== Verification Checklist ==="
echo "□ All text lines displayed correctly"
echo "□ Different spacing values are visually distinct"
echo "□ Keyboard controls work as expected"
echo "□ Animation runs smoothly"
echo "□ Text remains readable at all spacing values"
echo "□ No rendering artifacts or glitches"
echo
echo "If all items above are working correctly, the letter spacing"
echo "functionality is working as expected!"
