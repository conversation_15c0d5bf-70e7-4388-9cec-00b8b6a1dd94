# UIText Letter Spacing Test Program

This enhanced test program demonstrates the letter spacing functionality of the UIText library with comprehensive examples and interactive controls.

## Features

### Multiple Letter Spacing Examples
The program displays 7 different text objects, each demonstrating different letter spacing values:

1. **Title Text**: "UIText Letter Spacing Demo" (yellow)
2. **Normal Spacing (1.0)**: Baseline spacing (white)
3. **Tight Spacing (0.7)**: Closer character spacing (green)
4. **Loose Spacing (1.3)**: Wider character spacing (red)
5. **Very Tight Spacing (0.4)**: Very close characters (light blue)
6. **Very Loose Spacing (1.6)**: Very wide character spacing (pink)
7. **Animated Spacing**: Sine wave animation between 0.5 and 1.5 (magenta)

### Interactive Controls
- **Key 1**: Set all text to very tight spacing (0.3)
- **Key 2**: Set all text to normal spacing (1.0)
- **Key 3**: Set all text to very loose spacing (2.0)
- **Key R**: Reset to original spacing values
- **SPACE**: Reset animation timer
- **ESC**: Quit the program

### Visual Comparison
All text lines use the same alphabet sequence "ABCDEFGHIJKLMNOP" to make it easy to compare the visual differences between different letter spacing values.

## Technical Implementation

### Letter Spacing Function
The program uses the `UIText_SetLetterSpacing()` function to adjust character spacing:

```c
UIText_SetLetterSpacing(text_object, spacing_value);
```

Where `spacing_value` is a float multiplier:
- `1.0` = Normal spacing
- `< 1.0` = Tighter spacing
- `> 1.0` = Looser spacing

### Animation System
The animated text uses a sine wave function to continuously change letter spacing:

```c
context->current_animated_spacing = 1.0f + 0.5f * sinf(context->animation_time * 2.0f);
UIText_SetLetterSpacing(context->animated_text, context->current_animated_spacing);
```

This creates a smooth animation between 0.5 and 1.5 spacing values.

### Rendering Pipeline
The program follows the UIText library's two-phase rendering approach:

1. **Prepare Phase** (outside render pass):
   ```c
   UIText_PrepareTextData(context->text_context, texts, text_count, cmd_buf);
   ```

2. **Render Phase** (inside render pass):
   ```c
   UIText_RenderInRenderPass(context->text_context, texts, text_count, render_pass, cmd_buf);
   ```

## Building and Running

```bash
# Build the project
cd build
ninja

# Run the test program
./sdl3_pong
```

## Command Line Options

- `--no-sdf`: Disable SDF (Signed Distance Field) rendering

## Expected Output

The program displays a window with multiple lines of text, each demonstrating different letter spacing values. The bottom line shows an animated text where the letter spacing continuously changes in a sine wave pattern.

Use the keyboard controls to interactively test different spacing values and observe how the text appearance changes in real-time.

## Use Cases

This test program is useful for:
- **UI Design**: Testing optimal letter spacing for different UI elements
- **Typography**: Comparing readability at different spacing values
- **Animation**: Demonstrating dynamic letter spacing effects
- **Development**: Verifying UIText library letter spacing functionality
- **Performance**: Testing rendering performance with multiple text objects

## Notes

- All text objects are rendered in a single render pass for optimal performance
- The program uses SDF rendering by default for crisp text at any scale
- Letter spacing values are applied immediately and take effect in the next frame
- The animation runs at 60 FPS for smooth visual effects
