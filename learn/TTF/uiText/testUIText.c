#define SDL_MAIN_USE_CALLBACKS 1
#include <SDL3/SDL.h>
#include <SDL3/SDL_main.h>
#include <SDL3_ttf/SDL_ttf.h>
#include <math.h>
#include "UIText.h"

#define SUPPORTED_SHADER_FORMATS (SDL_GPU_SHADERFORMAT_SPIRV | SDL_GPU_SHADERFORMAT_DXIL | SDL_GPU_SHADERFORMAT_MSL)

typedef struct Context
{
	// Window and device
	SDL_Window *window;
	SDL_GPUDevice *device;
	float last_tick;
	float delta_time;
	bool app_in_background;
	bool running;

	// Text rendering using UIText library
	UITextContext *text_context;
	TTF_Font *font;

	// Multiple text objects for testing different letter spacing
	UITextObject *title_text;
	UITextObject *normal_spacing_text;
	UITextObject *tight_spacing_text;
	UITextObject *loose_spacing_text;
	UITextObject *very_tight_text;
	UITextObject *very_loose_text;
	UITextObject *animated_text;
	UITextObject *instructions_text;

	bool use_SDF;
	const char *font_filename;

	// Window dimensions
	int window_width;
	int window_height;

	// Animation for letter spacing
	float animation_time;
	float current_animated_spacing;
} Context;

void check_error_bool(const bool res)
{
	if (!res)
	{
		SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "%s", SDL_GetError());
	}
}

void *check_error_ptr(void *ptr)
{
	if (!ptr)
	{
		SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "%s", SDL_GetError());
	}
	return ptr;
}

int init(Context *context)
{
	check_error_bool(SDL_Init(SDL_INIT_VIDEO | SDL_INIT_EVENTS));

	// Create window and GPU device
	context->window_width = 800;
	context->window_height = 600;
	context->window = check_error_ptr(SDL_CreateWindow("2D Text Rendering", context->window_width, context->window_height, 0));
	context->device = check_error_ptr(SDL_CreateGPUDevice(SUPPORTED_SHADER_FORMATS, true, NULL));
	check_error_bool(SDL_ClaimWindowForGPUDevice(context->device, context->window));

	// Initialize TTF
	check_error_bool(TTF_Init());
	context->font = check_error_ptr(TTF_OpenFont(context->font_filename, 24));
	if (!context->font)
	{
		return -1;
	}

	SDL_Log("SDF %s", context->use_SDF ? "enabled" : "disabled");

	// Create UIText context
	context->text_context = UIText_CreateContext(context->device, context->window, context->window_width, context->window_height, context->use_SDF);
	if (!context->text_context)
	{
		SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Failed to create UIText context");
		return -1;
	}

	// Create multiple text objects to demonstrate different letter spacing values
	float y_pos = 30.0f;
	float line_height = 45.0f;

	// Title text
	context->title_text = UIText_CreateText(context->text_context, context->font, "UIText Letter Spacing Demo", 50.0f, y_pos, (SDL_FColor){1.0f, 1.0f, 0.0f, 1.0f});
	y_pos += line_height + 10.0f;

	// Normal spacing (1.0) - baseline
	context->normal_spacing_text = UIText_CreateText(context->text_context, context->font, "Normal: ABCDEFGHIJKLMNOP (1.0)", 50.0f, y_pos, (SDL_FColor){1.0f, 1.0f, 1.0f, 1.0f});
	UIText_SetLetterSpacing(context->normal_spacing_text, 1.0f);
	y_pos += line_height;

	// Tight spacing (0.7)
	context->tight_spacing_text = UIText_CreateText(context->text_context, context->font, "Tight:  ABCDEFGHIJKLMNOP (0.7)", 50.0f, y_pos, (SDL_FColor){0.5f, 1.0f, 0.5f, 1.0f});
	UIText_SetLetterSpacing(context->tight_spacing_text, 0.7f);
	y_pos += line_height;

	// Loose spacing (1.3)
	context->loose_spacing_text = UIText_CreateText(context->text_context, context->font, "Loose:  ABCDEFGHIJKLMNOP (1.3)", 50.0f, y_pos, (SDL_FColor){1.0f, 0.5f, 0.5f, 1.0f});
	UIText_SetLetterSpacing(context->loose_spacing_text, 1.3f);
	y_pos += line_height;

	// Very tight spacing (0.4)
	context->very_tight_text = UIText_CreateText(context->text_context, context->font, "V.Tight: ABCDEFGHIJKLMNOP (0.4)", 50.0f, y_pos, (SDL_FColor){0.8f, 0.8f, 1.0f, 1.0f});
	UIText_SetLetterSpacing(context->very_tight_text, 0.4f);
	y_pos += line_height;

	// Very loose spacing (1.6)
	context->very_loose_text = UIText_CreateText(context->text_context, context->font, "V.Loose: ABCDEFGHIJKLMNOP (1.6)", 50.0f, y_pos, (SDL_FColor){1.0f, 0.8f, 0.8f, 1.0f});
	UIText_SetLetterSpacing(context->very_loose_text, 1.6f);
	y_pos += line_height;

	// Animated spacing text
	context->animated_text = UIText_CreateText(context->text_context, context->font, "Animated: ABCDEFGHIJKLMNOP (sine wave)", 50.0f, y_pos, (SDL_FColor){1.0f, 0.5f, 1.0f, 1.0f});
	context->animation_time = 0.0f;
	context->current_animated_spacing = 1.0f;
	y_pos += line_height + 20.0f;

	// Instructions text
	context->instructions_text = UIText_CreateText(context->text_context, context->font, "Keys: 1=VeryTight 2=Normal 3=VeryLoose R=Reset | Anchors: L=Left C=Center G=Right N=None", 50.0f, y_pos, (SDL_FColor){0.7f, 0.7f, 0.7f, 1.0f});
	UIText_SetLetterSpacing(context->instructions_text, 0.9f);

	// Check if all text objects were created successfully
	if (!context->title_text || !context->normal_spacing_text || !context->tight_spacing_text ||
	    !context->loose_spacing_text || !context->very_tight_text || !context->very_loose_text ||
	    !context->animated_text || !context->instructions_text)
	{
		SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Failed to create text objects");
		return -1;
	}

	context->running = true;
	context->app_in_background = false;
	return 0;
}

SDL_AppResult SDL_AppInit(void **appstate, int argc, char *argv[])
{
	Context *context = SDL_calloc(1, sizeof(Context));
	*appstate = context;

	context->font_filename = "assets/fonts/Poppins/Poppins-Regular.ttf";
	context->use_SDF = true;

	for (int i = 1; i < argc; ++i)
	{
		if (SDL_strcasecmp(argv[i], "--no-sdf") == 0)
		{
			context->use_SDF = false;
		}
	}

	if (init(context) < 0)
	{
		return SDL_APP_FAILURE;
	}
	return SDL_APP_CONTINUE;
}

SDL_AppResult SDL_AppEvent(void *appstate, SDL_Event *event)
{
	Context *context = (Context *)appstate;
	switch (event->type)
	{
	case SDL_EVENT_KEY_UP:
		if (event->key.key == SDLK_ESCAPE)
		{
			context->running = false;
		}
		else if (event->key.key == SDLK_SPACE)
		{
			// Reset animation
			context->animation_time = 0.0f;
			SDL_Log("Animation reset");
		}
		else if (event->key.key == SDLK_1)
		{
			// Set all text to very tight spacing
			UIText_SetLetterSpacing(context->normal_spacing_text, 0.3f);
			UIText_SetLetterSpacing(context->tight_spacing_text, 0.3f);
			UIText_SetLetterSpacing(context->loose_spacing_text, 0.3f);
			UIText_SetLetterSpacing(context->very_tight_text, 0.3f);
			UIText_SetLetterSpacing(context->very_loose_text, 0.3f);
			SDL_Log("All text set to very tight spacing (0.3)");
		}
		else if (event->key.key == SDLK_2)
		{
			// Set all text to normal spacing
			UIText_SetLetterSpacing(context->normal_spacing_text, 1.0f);
			UIText_SetLetterSpacing(context->tight_spacing_text, 1.0f);
			UIText_SetLetterSpacing(context->loose_spacing_text, 1.0f);
			UIText_SetLetterSpacing(context->very_tight_text, 1.0f);
			UIText_SetLetterSpacing(context->very_loose_text, 1.0f);
			SDL_Log("All text set to normal spacing (1.0)");
		}
		else if (event->key.key == SDLK_3)
		{
			// Set all text to very loose spacing
			UIText_SetLetterSpacing(context->normal_spacing_text, 2.0f);
			UIText_SetLetterSpacing(context->tight_spacing_text, 2.0f);
			UIText_SetLetterSpacing(context->loose_spacing_text, 2.0f);
			UIText_SetLetterSpacing(context->very_tight_text, 2.0f);
			UIText_SetLetterSpacing(context->very_loose_text, 2.0f);
			SDL_Log("All text set to very loose spacing (2.0)");
		}
		else if (event->key.key == SDLK_R)
		{
			// Reset to original spacing values
			UIText_SetLetterSpacing(context->normal_spacing_text, 1.0f);
			UIText_SetLetterSpacing(context->tight_spacing_text, 0.7f);
			UIText_SetLetterSpacing(context->loose_spacing_text, 1.3f);
			UIText_SetLetterSpacing(context->very_tight_text, 0.4f);
			UIText_SetLetterSpacing(context->very_loose_text, 1.6f);
			SDL_Log("Reset to original spacing values");
		}
		else if (event->key.key == SDLK_L)
		{
			// Set anchor to LEFT (first character stays fixed) and demonstrate with spacing change
			UIText_SetSpacingAnchor(context->normal_spacing_text, UITEXT_ANCHOR_LEFT);
			UIText_SetSpacingAnchor(context->tight_spacing_text, UITEXT_ANCHOR_LEFT);
			UIText_SetSpacingAnchor(context->loose_spacing_text, UITEXT_ANCHOR_LEFT);
			UIText_SetSpacingAnchor(context->very_tight_text, UITEXT_ANCHOR_LEFT);
			UIText_SetSpacingAnchor(context->very_loose_text, UITEXT_ANCHOR_LEFT);
			UIText_SetSpacingAnchor(context->animated_text, UITEXT_ANCHOR_LEFT);

			// Temporarily change spacing to make anchor effect visible
			UIText_SetLetterSpacing(context->normal_spacing_text, 1.5f);
			UIText_SetLetterSpacing(context->tight_spacing_text, 1.5f);
			UIText_SetLetterSpacing(context->loose_spacing_text, 1.5f);
			UIText_SetLetterSpacing(context->very_tight_text, 1.5f);
			UIText_SetLetterSpacing(context->very_loose_text, 1.5f);

			SDL_Log("Anchor mode: LEFT (first character stays fixed) - spacing set to 1.5 to show effect");
		}
		else if (event->key.key == SDLK_C)
		{
			// Set anchor to CENTER (middle stays fixed) and demonstrate with spacing change
			UIText_SetSpacingAnchor(context->normal_spacing_text, UITEXT_ANCHOR_CENTER);
			UIText_SetSpacingAnchor(context->tight_spacing_text, UITEXT_ANCHOR_CENTER);
			UIText_SetSpacingAnchor(context->loose_spacing_text, UITEXT_ANCHOR_CENTER);
			UIText_SetSpacingAnchor(context->very_tight_text, UITEXT_ANCHOR_CENTER);
			UIText_SetSpacingAnchor(context->very_loose_text, UITEXT_ANCHOR_CENTER);
			UIText_SetSpacingAnchor(context->animated_text, UITEXT_ANCHOR_CENTER);

			// Temporarily change spacing to make anchor effect visible
			UIText_SetLetterSpacing(context->normal_spacing_text, 1.5f);
			UIText_SetLetterSpacing(context->tight_spacing_text, 1.5f);
			UIText_SetLetterSpacing(context->loose_spacing_text, 1.5f);
			UIText_SetLetterSpacing(context->very_tight_text, 1.5f);
			UIText_SetLetterSpacing(context->very_loose_text, 1.5f);

			SDL_Log("Anchor mode: CENTER (middle stays fixed) - spacing set to 1.5 to show effect");
		}
		else if (event->key.key == SDLK_G)
		{
			// Set anchor to RIGHT (last character stays fixed) and demonstrate with spacing change
			UIText_SetSpacingAnchor(context->normal_spacing_text, UITEXT_ANCHOR_RIGHT);
			UIText_SetSpacingAnchor(context->tight_spacing_text, UITEXT_ANCHOR_RIGHT);
			UIText_SetSpacingAnchor(context->loose_spacing_text, UITEXT_ANCHOR_RIGHT);
			UIText_SetSpacingAnchor(context->very_tight_text, UITEXT_ANCHOR_RIGHT);
			UIText_SetSpacingAnchor(context->very_loose_text, UITEXT_ANCHOR_RIGHT);
			UIText_SetSpacingAnchor(context->animated_text, UITEXT_ANCHOR_RIGHT);

			// Temporarily change spacing to make anchor effect visible
			UIText_SetLetterSpacing(context->normal_spacing_text, 1.5f);
			UIText_SetLetterSpacing(context->tight_spacing_text, 1.5f);
			UIText_SetLetterSpacing(context->loose_spacing_text, 1.5f);
			UIText_SetLetterSpacing(context->very_tight_text, 1.5f);
			UIText_SetLetterSpacing(context->very_loose_text, 1.5f);

			SDL_Log("Anchor mode: RIGHT (last character stays fixed) - spacing set to 1.5 to show effect");
		}
		else if (event->key.key == SDLK_N)
		{
			// Set anchor to NONE (all characters move proportionally)
			UIText_SetSpacingAnchor(context->normal_spacing_text, UITEXT_ANCHOR_NONE);
			UIText_SetSpacingAnchor(context->tight_spacing_text, UITEXT_ANCHOR_NONE);
			UIText_SetSpacingAnchor(context->loose_spacing_text, UITEXT_ANCHOR_NONE);
			UIText_SetSpacingAnchor(context->very_tight_text, UITEXT_ANCHOR_NONE);
			UIText_SetSpacingAnchor(context->very_loose_text, UITEXT_ANCHOR_NONE);
			UIText_SetSpacingAnchor(context->animated_text, UITEXT_ANCHOR_NONE);
			SDL_Log("Anchor mode: NONE (all characters move proportionally)");
		}
		break;
	case SDL_EVENT_QUIT:
		context->running = false;
		break;
	case SDL_EVENT_WILL_ENTER_BACKGROUND:
		context->app_in_background = true;
		break;
	case SDL_EVENT_DID_ENTER_FOREGROUND:
		context->app_in_background = false;
		break;
	}
	return context->running ? SDL_APP_CONTINUE : SDL_APP_SUCCESS;
}

int update(Context *context)
{
	float newTime = SDL_GetTicks() / 1000.0f;
	context->delta_time = newTime - context->last_tick;
	context->last_tick = newTime;

	// Update animation for the animated text
	context->animation_time += context->delta_time;

	// Create a sine wave animation for letter spacing (0.5 to 1.5 range)
	context->current_animated_spacing = 1.0f + 0.5f * sinf(context->animation_time * 2.0f);
	UIText_SetLetterSpacing(context->animated_text, context->current_animated_spacing);

	return 0;
}

int render(Context *context)
{
	SDL_GPUCommandBuffer *cmd_buf = check_error_ptr(SDL_AcquireGPUCommandBuffer(context->device)); // only acquire once per frame

	// Collect all text objects for rendering
	UITextObject *texts[] = {
		context->title_text,
		context->normal_spacing_text,
		context->tight_spacing_text,
		context->loose_spacing_text,
		context->very_tight_text,
		context->very_loose_text,
		context->animated_text,
		context->instructions_text
	};
	int text_count = sizeof(texts) / sizeof(texts[0]);

	// Step 1: Prepare text data (must be done outside render pass)
	UIText_PrepareTextData(context->text_context, texts, text_count, cmd_buf);

	// Step 2: Acquire swapchain texture
	SDL_GPUTexture *swapchain_texture;
	if (!SDL_WaitAndAcquireGPUSwapchainTexture(cmd_buf, context->window, &swapchain_texture, NULL, NULL))
	{
		SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Failed to acquire swapchain texture");
		return -1;
	}

	if (swapchain_texture != NULL)
	{
		// Step 3: Begin render pass
		SDL_GPUColorTargetInfo color_target_info = {0};
		color_target_info.texture = swapchain_texture;
		color_target_info.clear_color = (SDL_FColor){0.3f, 0.4f, 0.5f, 1.0f}; // Blue-gray background
		color_target_info.load_op = SDL_GPU_LOADOP_CLEAR;
		color_target_info.store_op = SDL_GPU_STOREOP_STORE;

		SDL_GPURenderPass *render_pass = SDL_BeginGPURenderPass(cmd_buf, &color_target_info, 1, NULL);

		// Step 4: Render text within the render pass
		UIText_RenderInRenderPass(context->text_context, texts, text_count, render_pass, cmd_buf);

		SDL_EndGPURenderPass(render_pass);
	}

	SDL_SubmitGPUCommandBuffer(cmd_buf);

	return 0;
}

SDL_AppResult SDL_AppIterate(void *appstate)
{
	Context *context = (Context *)appstate;
	if (context->app_in_background)
	{
		SDL_Delay(100);
		return SDL_APP_CONTINUE;
	}

	if (update(context) < 0)
	{
		return SDL_APP_FAILURE;
	}

	if (render(context) < 0)
	{
		return SDL_APP_FAILURE;
	}

	return SDL_APP_CONTINUE;
}

void cleanup(Context *context)
{
	// Cleanup all text objects
	UIText_DestroyText(context->title_text);
	UIText_DestroyText(context->normal_spacing_text);
	UIText_DestroyText(context->tight_spacing_text);
	UIText_DestroyText(context->loose_spacing_text);
	UIText_DestroyText(context->very_tight_text);
	UIText_DestroyText(context->very_loose_text);
	UIText_DestroyText(context->animated_text);
	UIText_DestroyText(context->instructions_text);

	// Cleanup UIText context
	UIText_DestroyContext(context->text_context);

	// Cleanup font and TTF
	TTF_CloseFont(context->font);
	TTF_Quit();

	// Cleanup SDL resources
	SDL_ReleaseWindowFromGPUDevice(context->device, context->window);
	SDL_DestroyGPUDevice(context->device);
	SDL_DestroyWindow(context->window);
	SDL_Quit();
}

void SDL_AppQuit(void *appstate, SDL_AppResult result)
{
	Context *context = (Context *)appstate;
	(void)result;
	if (context)
	{
		cleanup(context);
		SDL_free(context);
	}
}